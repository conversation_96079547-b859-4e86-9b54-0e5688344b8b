@page "/interactive-auto-demo"

<PageTitle>Interactive Auto Demo</PageTitle>

<h1>Interactive Auto Render Mode Demo</h1>

<div class="alert alert-info">
    <h4>Interactive Auto Render Mode</h4>
    <p>This page demonstrates the Interactive Auto render mode which:</p>
    <ul>
        <li><strong>Initially:</strong> Uses Interactive Server rendering (SignalR)</li>
        <li><strong>After WebAssembly loads:</strong> Switches to client-side WebAssembly rendering</li>
    </ul>
</div>

<div class="row">
    <div class="col-md-6">
        <h3>Component Instance 1</h3>
        <InteractiveCounterComponent InitialCount="0" />
    </div>
    <div class="col-md-6">
        <h3>Component Instance 2</h3>
        <InteractiveCounterComponent InitialCount="50" />
    </div>
</div>

<div class="mt-4">
    <div class="alert alert-warning">
        <h5>How to Test:</h5>
        <ol>
            <li>Open browser developer tools (F12)</li>
            <li>Go to Network tab</li>
            <li>Refresh this page</li>
            <li>Initially, button clicks will use SignalR (server-side)</li>
            <li>After WebAssembly loads, clicks will be handled client-side</li>
            <li>You can see the transition in the network activity</li>
        </ol>
    </div>
</div>

<div class="mt-4">
    <p class="text-muted">
        <strong>Note:</strong> This page is located in the Solnova.Client project. 
        The Interactive Auto render mode is automatically applied by the unified rendering system.
        Pages in the .Client project automatically use Interactive Auto or Interactive WebAssembly rendering.
    </p>
</div>
