<div class="card">
    <div class="card-body text-center">
        <h5 class="card-title">Interactive Counter (Auto Render)</h5>
        <p class="card-text">Current count: <strong>@currentCount</strong></p>
        <div class="btn-group" role="group">
            <button class="btn btn-primary" @onclick="IncrementCount">+1</button>
            <button class="btn btn-secondary" @onclick="DecrementCount">-1</button>
            <button class="btn btn-warning" @onclick="ResetCount">Reset</button>
        </div>
        <div class="mt-2">
            <small class="text-muted">This component uses Interactive Auto render mode (Server → WebAssembly)</small>
        </div>
        @if (lastAction != null)
        {
            <div class="mt-2">
                <small class="text-info">Last action: @lastAction at @lastActionTime?.ToString("HH:mm:ss")</small>
            </div>
        }
    </div>
</div>
