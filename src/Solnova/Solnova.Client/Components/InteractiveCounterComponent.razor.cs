using Microsoft.AspNetCore.Components;

namespace Solnova.Client.Components;

public partial class InteractiveCounterComponent : ComponentBase
{
    [Parameter] public int InitialCount { get; set; } = 0;
    
    private int currentCount = 0;
    private string? lastAction = "None";
    private DateTime? lastActionTime = DateTime.Now;

    protected override void OnInitialized()
    {
        currentCount = InitialCount;
        lastActionTime = DateTime.Now;
    }

    private void IncrementCount()
    {
        currentCount++;
        lastAction = "Increment";
        lastActionTime = DateTime.Now;
    }

    private void DecrementCount()
    {
        currentCount--;
        lastAction = "Decrement";
        lastActionTime = DateTime.Now;
    }

    private void ResetCount()
    {
        currentCount = 0;
        lastAction = "Reset";
        lastActionTime = DateTime.Now;
    }
}
